import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined, ensureDefined } from '@/utils/typeHelpers';
import { customerService, Customer, CustomerStats } from '@/services/customerService';
import { chatService } from '@/services/chatService';
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Loader2, Mail, MessageSquare, Phone, Trash2, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from 'sonner';

// Define conversation message type locally
interface ConversationMessage {
  id: string;
  message: string;
  sender_type: 'admin' | 'customer' | 'provider' | 'user';
  created_at: string;
}

// Define message type for display
interface Message {
  id: string;
  content: { type: string; data: string }[];
  sender: 'user' | 'bot';
  timestamp: Date;
}

interface CustomersTableProps {
  isMobile: boolean;
}

export const CustomersTable: React.FC<CustomersTableProps> = ({ isMobile }) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerStats, setCustomerStats] = useState<CustomerStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isConversationOpen, setIsConversationOpen] = useState(false);
  const [conversationMessages, setConversationMessages] = useState<Message[]>([]);
  const [isCreatingChat, setIsCreatingChat] = useState<string | null>(null);
  const authHeader = useAuthHeader();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch customers
  useEffect(() => {
    const fetchCustomers = async () => {
      setIsLoading(true);
      try {
        const response = await customerService.getCustomers(1, 10, undefined, nullToUndefined(authHeader) || '');
        
        if (response.isSuccess && response.data) {
          setCustomers(response.data.data);
        } else {
          toast.error(response.error || 'Failed to fetch customers');
        }
      } catch (error) {
        console.error('Error fetching customers:', error);
        toast.error('Failed to fetch customers');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomers();
  }, [authHeader]);

  // Fetch customer stats
  const fetchCustomerStats = async (customerId: string) => {
    try {
      const response = await customerService.getCustomerStats(customerId, nullToUndefined(authHeader) || '');
      
      if (response.isSuccess && response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching customer stats:', error);
      return null;
    }
  };

  const formatDate = (dateInput: string | Date): string => {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleViewDetails = async (customer: Customer) => {
    setSelectedCustomer(customer);
    setLoadingStats(true);

    const stats = await fetchCustomerStats(customer.id);
    if (stats) {
      setCustomerStats(stats);
    }

    setLoadingStats(false);
    setIsDetailModalOpen(true);
  };

  // Handle chat creation with customer
  const handleCreateChat = async (customer: Customer) => {
    setIsCreatingChat(customer.id);

    try {
      const response = await chatService.createChat(
        customer.id,
        'direct',
        undefined,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess && response.data) {
        toast({
          title: "Success",
          description: `Chat room created with ${customer.name}`,
        });
        navigate('/admin/messages');
      } else {
        if (response.error?.includes('already exists') || response.error?.includes('duplicate')) {
          toast({
            title: "Success",
            description: `Opening existing chat with ${customer.name}`,
          });
          navigate('/admin/messages');
        } else {
          toast({
            title: "Error",
            description: response.error || 'Failed to create chat room',
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      toast({
        title: "Error",
        description: 'Failed to create chat room',
        variant: "destructive"
      });
    } finally {
      setIsCreatingChat(null);
    }
  };

  // Handle customer deletion
  const handleDeleteCustomer = async () => {
    if (!customerToDelete) return;

    setIsDeleting(true);
    try {
      const response = await customerService.deleteCustomer(
        customerToDelete.id,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess) {
        toast.success('Customer deleted successfully');
        setCustomers(customers.filter(c => c.id !== customerToDelete.id));
        setIsDeleteModalOpen(false);
        setCustomerToDelete(null);
      } else {
        toast.error(response.error || 'Failed to delete customer');
      }
    } catch (error) {
      console.error('Error deleting customer:', error);
      toast.error('Failed to delete customer');
    } finally {
      setIsDeleting(false);
    }
  };

  const getUserInitials = (name: string): string => {
    return name.split(' ').map(name => name.charAt(0).toUpperCase()).join('');
  };

  const MobileCustomerCard = ({ customer }: { customer: Customer }) => {
    
    return (
      <Card className="mb-4">
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <Avatar>
              <AvatarImage src="/placeholder.svg" alt={customer.name} />
              <AvatarFallback>{getUserInitials(customer.name)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg font-semibold">{customer.name}</CardTitle>
              <CardDescription className="text-gray-500">
                {customer.email}
              </CardDescription>
            </div>
          </div>
          <div className="mt-3 flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleViewDetails(customer)}
              className="flex-1"
            >
              View Details
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCreateChat(customer)}
              disabled={isCreatingChat === customer.id}
              className="flex-1"
            >
              {isCreatingChat === customer.id ? (
                <>
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Chat
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setCustomerToDelete(customer);
                setIsDeleteModalOpen(true);
              }}
              className="px-2"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const tableRowContent = customers.map((customer) => (
    <TableRow key={customer.id}>
      <TableCell className="font-medium">{customer.id}</TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <Avatar>
            <AvatarImage src="/placeholder.svg" alt={customer.name} />
            <AvatarFallback>{getUserInitials(customer.name)}</AvatarFallback>
          </Avatar>
          <span>{customer.name}</span>
        </div>
      </TableCell>
      <TableCell>{customer.email}</TableCell>
      <TableCell>{customer.phone}</TableCell>
      <TableCell>
        {customer.last_login ? formatDate(customer.last_login) : 'N/A'}
      </TableCell>
      <TableCell>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(customer)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCreateChat(customer)}
            disabled={isCreatingChat === customer.id}
          >
            {isCreatingChat === customer.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <MessageSquare className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setCustomerToDelete(customer);
              setIsDeleteModalOpen(true);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  ));

  // Fix the conversation display
  const handleViewConversation = (customer: Customer) => {
    if (customer.conversation_history) {
      const messages: Message[] = customer.conversation_history.map((msg: ConversationMessage) => ({
        id: msg.id,
        content: [{ type: 'text', data: msg.message }],
        sender: msg.sender_type === 'customer' || msg.sender_type === 'user' ? 'user' : 'bot',
        timestamp: new Date(msg.created_at),
      }));
      
      setConversationMessages(messages);
      setIsConversationOpen(true);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Customers</CardTitle>
          <CardDescription>
            Manage registered customers and their details.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              {isMobile ? (
                <div className="space-y-4">
                  {customers.map((customer) => (
                    <MobileCustomerCard key={customer.id} customer={customer} />
                  ))}
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[80px]">ID</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Phone</TableHead>
                        <TableHead>Last Login</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tableRowContent}
                    </TableBody>
                  </Table>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Customer Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Customer Details</DialogTitle>
            <DialogDescription>
              View detailed information about the selected customer.
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <ScrollArea className="h-[400px] w-full space-y-4">
              <div className="py-4">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" alt={selectedCustomer.name} />
                    <AvatarFallback>{getUserInitials(selectedCustomer.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-lg font-semibold">{selectedCustomer.name}</div>
                    <div className="text-sm text-gray-500">{selectedCustomer.email}</div>
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span>ID: {selectedCustomer.id}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>Phone: {selectedCustomer.phone || 'N/A'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Email: {selectedCustomer.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Role ID: {selectedCustomer.role_id}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Created At: {selectedCustomer.created_at}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Updated At: {selectedCustomer.updated_at}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Last Login: {selectedCustomer.last_login}</span>
                  </div>
                </div>
              </div>

              {loadingStats ? (
                <div className="flex justify-center items-center p-8">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : customerStats ? (
                <div className="py-4 border-t">
                  <div className="text-lg font-semibold mb-2">Statistics</div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Total Orders</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">{customerStats.totalOrders}</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader>
                        <CardTitle>Total Spend</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {formatCurrency(customerStats.totalSpend)}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ) : (
                <div className="py-4 border-t text-center text-gray-500">
                  Could not load customer statistics.
                </div>
              )}
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Customer</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this customer? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {customerToDelete && (
              <div className="border rounded-md p-4">
                <div className="text-sm font-medium">
                  {customerToDelete.name}
                </div>
                <div className="text-sm text-gray-500">{customerToDelete.email}</div>
              </div>
            )}
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              disabled={isDeleting}
              onClick={handleDeleteCustomer}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Customer"
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Conversation Modal */}
      <Dialog open={isConversationOpen} onOpenChange={setIsConversationOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Conversation History</DialogTitle>
            <DialogDescription>
              View the conversation history with the selected customer.
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[400px] w-full space-y-4">
            {/* Conversation display will go here */}
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
};
